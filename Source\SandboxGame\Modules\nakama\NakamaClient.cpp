#include "NakamaClient.h"
#include "OgreScriptLuaVM.h"
#include "ScriptVM/LuaCall.h"

NakamaClient::NakamaClient(const std::string& serverKey, const std::string& host, int port, bool ssl):
    _Session(nullptr)
{
    Nakama::NLogger::initWithConsoleSink(Nakama::NLogLevel::Warn);
    params.serverKey = serverKey;
    params.host = host;
    params.port = port;
    params.ssl = ssl;

    LOG_WARNING("NakamaClient::NakamaClient %s %s %d %d", host.c_str(), serverKey.c_str(), port,(int)ssl);

    _client = Nakama::createDefaultClient(params);
}

NakamaClient::~NakamaClient()
{
    if (_client) {
        _client->disconnect();
    }
}

void NakamaClient::SetLuaCallback(const std::string& fun)
{
    _LuaCallback = fun;

    //MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]i", this, msgdata.bytes, msgdata.len);
}

void NakamaClient::AuthenticateSteam(const std::string& token, const std::string& username)
{
    _client->authenticateSteam(token, username, true ,{}, [this, token](Nakama::NSessionPtr Session) {
        _Session = Session;
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii", this, (int)AuthentMsg, (int)CallbackOK);
        CrateRTClient();
    }, 
    [this](const Nakama::NError& error) {
            LOG_WARNING("error code = %d", (int)error.code);

        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS", this, 
            (int)AuthentMsg, (int)CallbackError, error.message.c_str(), error.message.length());
    });
}

void NakamaClient::AuthenticateEmail(const std::string email, const std::string& passwd, const std::string& username)
{
    _client->authenticateEmail(email, passwd, username, true, {}, [this](Nakama::NSessionPtr Session) {
        _Session = Session;
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii", this, (int)AuthentMsg, (int)CallbackOK);
        CrateRTClient();
    },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS", this,
                (int)AuthentMsg, (int)CallbackError, error.message.c_str(), error.message.length());
    });
}

std::string NakamaClient::GetAuthToken()
{
    if (!_Session)
    {
        return "";
    }

    return _Session->getAuthToken();
}

void NakamaClient::CrateRTClient()
{
    _listener.setConnectCallback([this]() {
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii", 
            this, (int)CrateRTMsg, (int)CallbackOK);
    });

    _listener.setErrorCallback([this](const Nakama::NRtError& error) {
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS", 
            this, (int)CrateRTMsg, (int)CallbackError, error.message.c_str(), error.message.length());
    });

    _listener.setNotificationsCallback([this](const Nakama::NNotificationList& notificationList) {
        jsonxx::Array arr;

        for (Nakama::NNotification n : notificationList.notifications)
        {
            arr.import(NNotification2JsonObj(n));
        }

        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
            this, (int)NotificationsMsg, (int)CallbackOK, arr.json().c_str(), arr.json().length());
    });

    _rtClient = _client->createRtClient();
    _rtClient->setListener(&_listener);
    _rtClient->connect(_Session, true, Nakama::NRtClientProtocol::Json);
}

void NakamaClient::ListNotifications(int num)
{
    if (!_Session)
    {
        return;
    }

    _client->listNotifications(_Session, num, "", 
        [this](Nakama::NNotificationListPtr notificationList) {

            jsonxx::Array arr;

            for (const Nakama::NNotification& n : notificationList->notifications)
            {
                arr.import(NNotification2JsonObj(n));
            }

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListNotificationMsg, (int)CallbackOK, arr.json().c_str(), arr.json().length());
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListNotificationMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::DeleteNotification(const std::string& notificationId)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> notificationIds = { notificationId };
    _client->deleteNotifications(_Session, notificationIds, [this]() {
        MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii",
            this, (int)DeleteNotificationMsg, (int)CallbackOK);
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)DeleteNotificationMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::GetAccount()
{
    if (!_Session)
    {
        return;
    }

    _client->getAccount(_Session, [this](const Nakama::NAccount& account) {

            jsonxx::Object obj;

            obj << "user" << NUser2JsonObj(account.user);
            obj << "wallet" << account.wallet;
            obj << "email" << account.email;
            //obj << "devices" << account.devices;
            obj << "customId" << account.customId;
            obj << "verifyTime" << account.verifyTime;
            obj << "disableTime" << account.disableTime;

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)AccountMsg, (int)CallbackOK, obj.json().c_str(), obj.json().length());

        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)AccountMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::Rpc(const std::string& id, const std::string& payload)
{
    if (!_rtClient) return;

    _rtClient->rpc(id, payload, [this](const Nakama::NRpc& rpc) {

            jsonxx::Object obj;
            obj << "id" << rpc.id;
            obj << "payload" << rpc.payload;
            obj << "httpKey" << rpc.httpKey;

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)RPCMsg, (int)CallbackOK, obj.json().c_str(), obj.json().length());

        }, 
        [this](const Nakama::NRtError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)RPCMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::AddFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->addFriends(_Session, ids, {},
        [this]() {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii",
                this, (int)AddFriendMsg, (int)CallbackOK);
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)AddFriendMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::DeleteFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->deleteFriends(_Session, ids, {},
        [this]() {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii",
                this, (int)DeleteFriendMsg, (int)CallbackOK);
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)DeleteFriendMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::BlockFriend(const std::string& id)
{
    if (!_Session)
    {
        return;
    }

    std::vector<std::string> ids = { id };
    _client->blockFriends(_Session, ids, {},
        [this]() {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]ii",
                this, (int)BlockFriendMsg, (int)CallbackOK);
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)BlockFriendMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        });
}

void NakamaClient::ListFriend(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::FRIEND,
        "",
        [this](Nakama::NFriendListPtr list){

            jsonxx::Array arr;

            for (const Nakama::NFriend &it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListFriendMsg, (int)CallbackOK, arr.json().c_str(), arr.json().length());
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListFriendMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        }
    );
}

void NakamaClient::ListFriendRequest(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::INVITE_RECEIVED,
        "",
        [this](Nakama::NFriendListPtr list) {

            jsonxx::Array arr;

            for (const Nakama::NFriend& it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListFriendRequestMsg, (int)CallbackOK, arr.json().c_str(), arr.json().length());
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)ListFriendRequestMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        }
    );
}

void NakamaClient::ListBlockFriend(int limit)
{
    if (!_Session)
    {
        return;
    }

    _client->listFriends(
        _Session,
        limit,
        Nakama::NFriend::State::BLOCKED,
        "",
        [this](Nakama::NFriendListPtr list) {

            jsonxx::Array arr;

            for (const Nakama::NFriend& it : list->friends) {
                jsonxx::Object obj;

                obj << "user" << NUser2JsonObj(it.user);
                obj << "state" << (int)it.state;
                obj << "updateTime" << it.updateTime;

                arr.import(obj);
            }

            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)BlockListFriendMsg, (int)CallbackOK, arr.json().c_str(), arr.json().length());
        },
        [this](const Nakama::NError& error) {
            MINIW::ScriptVM::game()->callFunction(_LuaCallback.c_str(), "u[NakamaClient]iiS",
                this, (int)BlockListFriendMsg, (int)CallbackError, error.message.c_str(), error.message.length());
        }
    );
}

void NakamaClient::JoinRoom(const std::string& roomName)
{

}

void NakamaClient::JoinGroup(const std::string& groupId)
{

}

void NakamaClient::JoinDirectMessage(const std::string& userId)
{

}

void NakamaClient::WriteChatMessage(const std::string& channelId, const std::string& content)
{

}

void NakamaClient::ListChannelMessages(const std::string groupId, int linit)
{

}

void NakamaClient::Processing()
{
    if (!_client) {
        return;
    }
    _client->tick();

    if (_rtClient) _rtClient->tick();

    if (_Session && (_Session->isExpired() || _Session->isExpired(time(0) + 24 * 60 * 60)))
    {
        //auto refreshSuccessCallback = [this](Nakama::NSessionPtr session)
        //    {
        //        _Session = session;
        //    };
        //
        //auto refreshErrorCallback = [](const Nakama::NError& error)
        //    {
        //        LOG_WARNING("refresh error");
        //    };

        // Refresh the existing session
        //_client->authenticateRefresh(_Session, refreshSuccessCallback, refreshErrorCallback);
        _Session = Nakama::restoreSession(_Session->getAuthToken(), _Session->getRefreshToken());
    }
}

jsonxx::Object NakamaClient::NUser2JsonObj(const Nakama::NUser& user)
{
    jsonxx::Object ret;

    ret << "id" << user.id;
    ret << "username" << user.username;
    ret << "displayName" << user.displayName;
    ret << "avatarUrl" << user.avatarUrl;
    ret << "lang" << user.lang;
    ret << "location" << user.location;
    ret << "timeZone" << user.timeZone;
    ret << "metadata" << user.metadata;
    ret << "facebookId" << user.facebookId;
    ret << "googleId" << user.googleId;
    ret << "gameCenterId" << user.gameCenterId;
    ret << "appleId" << user.appleId;
    ret << "steamId" << user.steamId;
    ret << "online" << user.online;
    ret << "edgeCount" << user.edgeCount;
    ret << "createdAt" << user.createdAt;
    ret << "updatedAt" << user.updatedAt;

    return ret;
}

jsonxx::Object NakamaClient::NNotification2JsonObj(const Nakama::NNotification& n)
{
    jsonxx::Object itemobj;

    itemobj << "id" << n.id;
    itemobj << "subject" << n.subject;
    itemobj << "content" << n.content;
    itemobj << "code" << n.code;
    itemobj << "senderId" << n.senderId;
    itemobj << "createTime" << n.createTime;
    itemobj << "persistent" << n.persistent;

    return itemobj;
}

void NakamaClient::UpdataName(const std::string& Name)
{
    if (!_Session)
        return;

    _client->getAccount(_Session, [this, Name](const Nakama::NAccount& account) {
        _client->updateAccount(_Session,
            account.user.username,
            Name,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt,
            Nakama::opt::nullopt);
        },
        [this](const Nakama::NError& error) {
            
        });
}

bool NakamaClient::IsCreate()
{
    if (!_Session)
        return false;

    return _Session->isCreated();
}

void NakamaClient::StartProcess()
{
    GetSandBoxManager().SetTimer(0, 100, this);
}

void NakamaClient::OnTimer(unsigned long dwTimerID)
{
    Processing();
}
